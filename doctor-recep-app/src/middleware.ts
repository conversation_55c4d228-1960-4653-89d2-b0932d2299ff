import { NextResponse, type NextRequest } from 'next/server'
import { decrypt } from '@/lib/auth/session'

// This middleware will run on every request.
export async function middleware(request: NextRequest) {
// We don't need to do anything for API routes or static files.
if (request.nextUrl.pathname.startsWith('/api/') || request.nextUrl.pathname.startsWith('/_next/')) {
return NextResponse.next();
}

// Get our custom 'session' cookie.
const sessionCookie = request.cookies.get('session')?.value;
if (!sessionCookie) {
return NextResponse.next();
}

// Decrypt the session to get the payload with the userId.
const session = await decrypt(sessionCookie);

// If we have a valid session and userId, create a new set of request headers.
const requestHeaders = new Headers(request.headers);

// Set a special header that our Supabase client will use.
// This header will contain the user's ID.
if (session?.userId) {
requestHeaders.set('X-User-ID', session.userId);
}

// Continue the request, but with our new, modified headers.
return NextResponse.next({
request: {
headers: requestHeaders,
},
});
}

export const config = {
matcher: '/((?!_next/static|_next/image|favicon.ico).*)',
};