import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { Database } from '@/lib/types'

// This function creates a Supabase client that is authenticated with the
// SERVICE_ROLE_KEY. This key has super-admin privileges and bypasses all RLS policies.
//
// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
// !!! DANGER: This client should ONLY be used in trusted server-side code.  !!!
// !!! NEVER expose this client or the SERVICE_ROLE_KEY to the browser.    !!!
// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
//
// It is required for actions that need to operate outside of a user's
// permissions, such as looking up a user by email during the login process.

export async function createServiceRoleClient() {
  // We use the 'cookies' store from next/headers to maintain consistency with other clients,
  // even though for the service role, session management is less critical since it bypasses RLS.
  const cookieStore = await cookies()

  return createServerClient<Database>(
    // Arg 1: The public URL of your Supabase project.
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    // Arg 2: The SERVICE_ROLE_KEY. This is the "master key" that grants this client
    //        the power to bypass RLS. It MUST be stored securely in your .env.local
    //        and should never be prefixed with NEXT_PUBLIC_.
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    // Arg 3: The cookie handling implementation. This tells the Supabase client
    //        how to read, write, and delete cookies on the server.
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )
}