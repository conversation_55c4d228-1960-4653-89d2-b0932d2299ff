import { createServerClient } from '@supabase/ssr'
import { cookies, headers } from 'next/headers' // Import 'headers'
import { Database } from '@/lib/types'

export async function createClient() {
  const cookieStore = cookies()
  const headerStore = headers() // Get the headers object

  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // ...
          }
        },
      },
      // THIS IS THE NEW PART:
      // We check if our custom 'X-User-ID' header exists.
      // If it does, we set a PostgreSQL configuration variable for the
      // duration of this transaction. Our RLS policies will read this variable.
      db: {
        async prepare(connection) {
          const userId = headerStore.get('X-User-ID');
          if (userId) {
            await connection.query(`SELECT set_config('request.user_id', '${userId}', false);`);
          }
        }
      }
    }
  )
}