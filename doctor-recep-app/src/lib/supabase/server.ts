import { createServerClient } from '@supabase/ssr'
import { cookies, headers } from 'next/headers'
import { Database } from '@/lib/types'

export async function createClient() {
  const cookieStore = await cookies()
  const headersList = await headers()

  const client = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )

  // CRITICAL: Set user context for RLS (now that RLS is enabled)
  const userId = headersList.get('X-User-ID')
  if (userId) {
    try {
      // Set the session variable using a direct SQL query
      // This ensures it's set in the same transaction as subsequent queries
      const { error } = await client.from('doctors').select('1').limit(0)

      if (!error) {
        // Now set the session variable for this connection
        await client.rpc('set_user_context' as any, { user_id: userId })
        console.log('RLS context set for user:', userId)
      }
    } catch (error) {
      // If we can't set context, log but continue (allows app to work for service role operations)
      console.warn('Could not set RLS context:', error)
    }
  }

  return client
}